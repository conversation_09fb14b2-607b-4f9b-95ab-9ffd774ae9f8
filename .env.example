# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# For local development with Docker Compose, these will be set automatically
# For production or standalone development, set these to your Supabase project values

VITE_SUPABASE_URL=http://localhost:8000
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API URL
VITE_API_URL=http://localhost:3001

# =============================================================================
# PRODUCTION OVERRIDE EXAMPLE
# =============================================================================
# For production, replace with your hosted Supabase credentials:
# VITE_SUPABASE_URL=https://your-project.supabase.co
# VITE_SUPABASE_ANON_KEY=your_production_anon_key