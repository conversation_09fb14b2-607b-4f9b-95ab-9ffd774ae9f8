import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useAuth } from './AuthContext';
import { apiClient, isApiError } from '@/lib/api-client';
import type { 
  UpdateWorkspaceRequest,
  WorkspaceInvitation,
  CreateWorkspaceInvitationRequest,
  UpdateWorkspaceInvitationRequest
} from '@/lib/api-client';
import {
  WorkspaceContextType,
  Workspace,
  UserProfile,
  UserRole,
  WorkspaceError,
  WorkspaceErrorCode,
  SubscriptionPlan,
  WorkspaceStatus,
} from '@/types';

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined);

export const useWorkspace = () => {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider');
  }
  return context;
};

interface WorkspaceProviderProps {
  children: React.ReactNode;
}

export const WorkspaceProvider: React.FC<WorkspaceProviderProps> = ({ children }) => {
  const { user, profile, workspace: authWorkspace, accessToken } = useAuth();
  const [workspace, setWorkspace] = useState<Workspace | null>(authWorkspace);
  const [teamMembers, setTeamMembers] = useState<UserProfile[]>([]);
  const [invitations, setInvitations] = useState<WorkspaceInvitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<WorkspaceError | null>(null);

  // Helper function to create WorkspaceError
  const createWorkspaceError = useCallback((error: any, code?: WorkspaceErrorCode): WorkspaceError => {
    let errorCode = code || WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED;
    let message = 'An unknown workspace error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Map common error patterns to our error codes
    if (message.includes('not found') || message.includes('does not exist')) {
      errorCode = WorkspaceErrorCode.WORKSPACE_NOT_FOUND;
    } else if (message.includes('suspended')) {
      errorCode = WorkspaceErrorCode.WORKSPACE_SUSPENDED;
    } else if (message.includes('trial expired')) {
      errorCode = WorkspaceErrorCode.TRIAL_EXPIRED;
    } else if (message.includes('subscription required')) {
      errorCode = WorkspaceErrorCode.SUBSCRIPTION_REQUIRED;
    } else if (message.includes('team limit')) {
      errorCode = WorkspaceErrorCode.TEAM_LIMIT_EXCEEDED;
    } else if (message.includes('listing limit')) {
      errorCode = WorkspaceErrorCode.LISTING_LIMIT_EXCEEDED;
    } else if (message.includes('storage limit')) {
      errorCode = WorkspaceErrorCode.STORAGE_LIMIT_EXCEEDED;
    } else if (message.includes('feature not available')) {
      errorCode = WorkspaceErrorCode.FEATURE_NOT_AVAILABLE;
    }

    return {
      code: errorCode,
      message,
      workspace_id: workspace?.id,
      timestamp: new Date().toISOString(),
    };
  }, [workspace?.id]);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Fetch team members (placeholder - not implemented in current API)
  const fetchTeamMembers = useCallback(async (workspaceId: string) => {
    try {
      // TODO: Implement team members API endpoint
      console.log('Team members fetching not implemented yet for workspace:', workspaceId);
      setTeamMembers([]);
    } catch (error) {
      console.error('Error fetching team members:', error);
      setError(createWorkspaceError(error));
    }
  }, [createWorkspaceError]);

  // Fetch workspace invitations using new API
  const fetchInvitations = useCallback(async (workspaceId: string) => {
    if (!accessToken) return;
    
    try {
      const response = await apiClient.getWorkspaceInvitations(accessToken, {
        page: 1,
        limit: 50,
        status: 'pending'
      });
      
      // Map API response to local WorkspaceInvitation type
      const mappedInvitations = (response.data || []).map(invitation => ({
        id: invitation.id,
        workspace_id: invitation.workspaceId,
        email: invitation.email,
        role: invitation.role,
        invited_by: invitation.invitedBy,
        status: invitation.status,
        token: '', // Not included in API response for security
        expires_at: invitation.expiresAt,
        created_at: invitation.createdAt,
        updated_at: invitation.updatedAt,
        accepted_at: null, // Only pending invitations are fetched
      }));
      
      setInvitations(mappedInvitations);
    } catch (error) {
      console.error('Error fetching invitations:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
    }
  }, [accessToken, createWorkspaceError]);

  // Update workspace function using new API
  const updateWorkspace = useCallback(async (data: Partial<Workspace>) => {
    if (!workspace?.id || !accessToken) {
      setError(createWorkspaceError('No workspace available', WorkspaceErrorCode.WORKSPACE_NOT_FOUND));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Map workspace data to API request format
      const request: UpdateWorkspaceRequest = {
        companyName: data.company_name,
        companyType: data.company_type as any,
        subscriptionPlan: data.subscription_plan as any,
        domain: data.domain,
        logoUrl: data.logo_url,
        primaryColor: data.primary_color || '#3B82F6',
        address: data.address,
        phone: data.phone,
        website: data.website,
        licenseNumber: data.license_number,
        specialties: data.specialties,
        targetMarkets: data.target_markets,
        status: data.status as any,
        onboardingCompleted: data.onboarding_completed || false,
        onboardingStep: data.onboarding_step || 1,
      };

      const response = await apiClient.updateCurrentWorkspace(accessToken, request);
      
      // Map API response back to local Workspace type
      const mappedWorkspace: Workspace = {
        id: response.data.id,
        company_name: response.data.companyName,
        company_type: response.data.companyType as any,
        subscription_plan: response.data.subscriptionPlan as any,
        domain: response.data.domain,
        logo_url: response.data.logoUrl,
        primary_color: response.data.primaryColor,
        address: response.data.address,
        phone: response.data.phone,
        website: response.data.website,
        license_number: response.data.licenseNumber,
        specialties: response.data.specialties,
        target_markets: response.data.targetMarkets,
        status: response.data.status as any,
        trial_ends_at: response.data.trialEndsAt,
        onboarding_completed: response.data.onboardingCompleted,
        onboarding_step: response.data.onboardingStep,
        created_at: response.data.createdAt,
        updated_at: response.data.updatedAt,
      };
      
      setWorkspace(mappedWorkspace);
    } catch (error) {
      console.error('Error updating workspace:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, accessToken, createWorkspaceError]);

  // Invite team member function using new API
  const inviteTeamMember = useCallback(async (email: string, role: UserRole) => {
    if (!workspace?.id || !user?.id || !accessToken) {
      setError(createWorkspaceError('No workspace or user available', WorkspaceErrorCode.WORKSPACE_NOT_FOUND));
      return;
    }

    // Check if user can invite (owner or admin)
    if (profile?.role !== 'owner' && profile?.role !== 'admin') {
      setError(createWorkspaceError('Insufficient permissions to invite team members', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    // Validate role (cannot invite owners)
    if (role === UserRole.OWNER) {
      setError(createWorkspaceError('Cannot invite users as owners', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Create invitation using new API
      const request: CreateWorkspaceInvitationRequest = {
        email,
        role: role as "owner" | "admin" | "member" | "viewer",
      };

      const newInvitation = await apiClient.createWorkspaceInvitation(accessToken, request);

      // Add to local state
      setInvitations(prev => [newInvitation, ...prev]);

      console.log('Invitation created:', newInvitation);

    } catch (error) {
      console.error('Error inviting team member:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, user?.id, profile?.role, accessToken, createWorkspaceError]);

  // Remove team member function (placeholder - not implemented in current API)
  const removeTeamMember = useCallback(async (userId: string) => {
    if (!workspace?.id || !accessToken) {
      setError(createWorkspaceError('No workspace available', WorkspaceErrorCode.WORKSPACE_NOT_FOUND));
      return;
    }

    // Check if user can remove team members (owner or admin)
    if (profile?.role !== 'owner' && profile?.role !== 'admin') {
      setError(createWorkspaceError('Insufficient permissions to remove team members', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    // Cannot remove self
    if (userId === user?.id) {
      setError(createWorkspaceError('Cannot remove yourself from the workspace', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // TODO: Implement remove team member API endpoint
      console.log('Remove team member not implemented yet for user:', userId);

      // Remove from local state (temporary)
      setTeamMembers(prev => prev.filter(member => member.id !== userId));

    } catch (error) {
      console.error('Error removing team member:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, profile?.role, user?.id, accessToken, createWorkspaceError]);

  // Update member role function (placeholder - not implemented in current API)
  const updateMemberRole = useCallback(async (userId: string, role: UserRole) => {
    if (!workspace?.id || !accessToken) {
      setError(createWorkspaceError('No workspace available', WorkspaceErrorCode.WORKSPACE_NOT_FOUND));
      return;
    }

    // Check if user can update roles (owner or admin)
    if (profile?.role !== 'owner' && profile?.role !== 'admin') {
      setError(createWorkspaceError('Insufficient permissions to update member roles', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    // Cannot update own role
    if (userId === user?.id) {
      setError(createWorkspaceError('Cannot update your own role', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // TODO: Implement update member role API endpoint
      console.log('Update member role not implemented yet for user:', userId, 'to role:', role);

      // Update local state (temporary)
      setTeamMembers(prev => 
        prev.map(member => 
          member.id === userId ? { ...member, role: role as any } : member
        )
      );

    } catch (error) {
      console.error('Error updating member role:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, profile?.role, user?.id, accessToken, createWorkspaceError]);

  // Resend invitation function using new API
  const resendInvitation = useCallback(async (invitationId: string) => {
    if (!workspace?.id || !accessToken) {
      setError(createWorkspaceError('No workspace available', WorkspaceErrorCode.WORKSPACE_NOT_FOUND));
      return;
    }

    // Check if user can manage invitations (owner or admin)
    if (profile?.role !== 'owner' && profile?.role !== 'admin') {
      setError(createWorkspaceError('Insufficient permissions to manage invitations', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const updatedInvitation = await apiClient.resendWorkspaceInvitation(accessToken, invitationId);

      // Update local state
      setInvitations(prev => 
        prev.map(invitation => 
          invitation.id === invitationId ? updatedInvitation : invitation
        )
      );

      console.log('Invitation resent:', updatedInvitation);

    } catch (error) {
      console.error('Error resending invitation:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, profile?.role, accessToken, createWorkspaceError]);

  // Revoke invitation function using new API
  const revokeInvitation = useCallback(async (invitationId: string) => {
    if (!workspace?.id || !accessToken) {
      setError(createWorkspaceError('No workspace available', WorkspaceErrorCode.WORKSPACE_NOT_FOUND));
      return;
    }

    // Check if user can manage invitations (owner or admin)
    if (profile?.role !== 'owner' && profile?.role !== 'admin') {
      setError(createWorkspaceError('Insufficient permissions to manage invitations', WorkspaceErrorCode.WORKSPACE_ACCESS_DENIED));
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await apiClient.deleteWorkspaceInvitation(accessToken, invitationId);

      // Remove from local state
      setInvitations(prev => prev.filter(invitation => invitation.id !== invitationId));

    } catch (error) {
      console.error('Error revoking invitation:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, profile?.role, accessToken, createWorkspaceError]);

  // Refresh team data function
  const refreshTeamData = useCallback(async () => {
    if (!workspace?.id) return;

    try {
      setLoading(true);
      setError(null);

      await Promise.all([
        fetchTeamMembers(workspace.id),
        fetchInvitations(workspace.id),
      ]);

    } catch (error) {
      console.error('Error refreshing team data:', error);
      if (isApiError(error)) {
        setError(createWorkspaceError(error));
      }
    } finally {
      setLoading(false);
    }
  }, [workspace?.id, fetchTeamMembers, fetchInvitations, createWorkspaceError]);

  // Sync workspace from AuthContext
  useEffect(() => {
    setWorkspace(authWorkspace);
  }, [authWorkspace]);

  // Load initial team data when workspace changes
  useEffect(() => {
    // Only fetch invitations when workspace is available and user has permissions
    if (workspace?.id && accessToken && (profile?.role === 'owner' || profile?.role === 'admin')) {
      fetchInvitations(workspace.id);
    } else {
      setTeamMembers([]);
      setInvitations([]);
    }
  }, [workspace?.id, accessToken, profile?.role, fetchInvitations]);

  // Monitor workspace status and trial expiration
  useEffect(() => {
    if (!workspace) return;

    const checkWorkspaceStatus = () => {
      const now = new Date();
      
      // Check trial expiration
      if (workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at) {
        const trialEndsAt = new Date(workspace.trial_ends_at);
        if (now > trialEndsAt) {
          setError(createWorkspaceError(
            'Your trial period has expired. Please upgrade your subscription to continue using the workspace.',
            WorkspaceErrorCode.TRIAL_EXPIRED
          ));
        }
      }

      // Check workspace suspension
      if (workspace.status === WorkspaceStatus.SUSPENDED) {
        setError(createWorkspaceError(
          'This workspace has been suspended. Please contact support for assistance.',
          WorkspaceErrorCode.WORKSPACE_SUSPENDED
        ));
      }
    };

    checkWorkspaceStatus();

    // Check status every 5 minutes
    const statusCheckInterval = setInterval(checkWorkspaceStatus, 5 * 60 * 1000);

    return () => clearInterval(statusCheckInterval);
  }, [workspace, createWorkspaceError]);

  const value: WorkspaceContextType = {
    workspace,
    teamMembers,
    invitations,
    loading,
    error,
    updateWorkspace,
    inviteTeamMember,
    removeTeamMember,
    updateMemberRole,
    resendInvitation,
    revokeInvitation,
    refreshTeamData,
    clearError,
  };

  return (
    <WorkspaceContext.Provider value={value}>
      {children}
    </WorkspaceContext.Provider>
  );
};