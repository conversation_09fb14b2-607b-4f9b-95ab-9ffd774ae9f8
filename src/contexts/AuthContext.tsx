import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { User } from '@/lib/api-client';
// import { supabase } from '@/lib/supabase';
import { apiClient, isApiError, type SignUpRequest } from '@/lib/api-client';
import { 
  AuthContextType, 
  UserProfile, 
  Workspace, 
  SignUpData, 
  AuthError, 
  AuthErrorCode 
} from '@/types';
import { handleTokenExpiration, isTokenExpiredError } from '@/lib/token-expiration-handler';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // All user data stored in memory only
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  // Helper function to create AuthError from various error types
  const createAuthError = useCallback((error: any, code?: AuthErrorCode): AuthError => {
    let errorCode = code || AuthErrorCode.UNKNOWN_ERROR;
    let message = 'An unknown error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Map common Supabase error messages to our error codes
    if (message.includes('Invalid login credentials')) {
      errorCode = AuthErrorCode.INVALID_CREDENTIALS;
    } else if (message.includes('Email not confirmed')) {
      errorCode = AuthErrorCode.EMAIL_NOT_VERIFIED;
    } else if (message.includes('Password should be at least')) {
      errorCode = AuthErrorCode.WEAK_PASSWORD;
    } else if (message.includes('User already registered')) {
      errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
    } else if (message.includes('Signups not allowed')) {
      errorCode = AuthErrorCode.SIGNUP_DISABLED;
    } else if (message.includes('network') || message.includes('fetch')) {
      errorCode = AuthErrorCode.NETWORK_ERROR;
    }

    return {
      code: errorCode,
      message,
      timestamp: new Date().toISOString(),
    };
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Token storage utilities - using httpOnly cookies for refresh token
  const storeRefreshToken = useCallback((refreshToken: string) => {
    if (!refreshToken || refreshToken === 'undefined' || refreshToken === 'null') {
      console.error('Invalid refresh token provided to storeRefreshToken:', refreshToken);
      return;
    }

    // Only use secure flag in production (HTTPS), not in development (HTTP localhost)
    const isProduction = import.meta.env.VITE_NODE_ENV === 'production';
    const isHttps = window.location.protocol === 'https:';
    const useSecure = isProduction && isHttps;
    
    const cookieAttributes = [
      `refresh_token=${refreshToken}`,
      'path=/',
      'samesite=strict',
      `max-age=${7 * 24 * 60 * 60}`, // 7 days
      ...(useSecure ? ['secure'] : [])
    ].join('; ');
    
    document.cookie = cookieAttributes;
    
    console.log('Stored refresh token with attributes:', cookieAttributes);
  }, []);

  const getRefreshToken = useCallback((): string | null => {
    const cookies = document.cookie.split(';');
    const refreshTokenCookie = cookies.find(cookie => cookie.trim().startsWith('refresh_token='));
    const token = refreshTokenCookie ? refreshTokenCookie.split('=')[1] : null;
    
    console.log('Getting refresh token from cookies:', {
      allCookies: document.cookie,
      foundCookie: refreshTokenCookie,
      extractedToken: token ? `${token.substring(0, 10)}...` : null
    });
    
    return token;
  }, []);

  const clearRefreshToken = useCallback(() => {
    // Clear the cookie by setting it to expire in the past
    const isProduction = import.meta.env.VITE_NODE_ENV === 'production';
    const isHttps = window.location.protocol === 'https:';
    const useSecure = isProduction && isHttps;
    
    const cookieAttributes = [
      'refresh_token=',
      'path=/',
      'expires=Thu, 01 Jan 1970 00:00:00 GMT',
      ...(useSecure ? ['secure'] : [])
    ].join('; ');
    
    document.cookie = cookieAttributes;
    
    console.log('Cleared refresh token with attributes:', cookieAttributes);
  }, [])

  // Clear all auth data from memory
  const clearAuthData = useCallback(() => {
    setUser(null);
    setProfile(null);
    setWorkspace(null);
    setAccessToken(null);
  }, []);

  // Fetch user profile and workspace data
  const fetchUserData = useCallback(async (userId: string) => {
    // DISABLED: Direct Supabase calls removed to prevent fetches on signin
    // The API client handles all data fetching instead
    console.log('fetchUserData called but disabled - using API client instead');
    
    // try {
    //   // Fetch user profile
    //   const { data: profileData, error: profileError } = await supabase
    //     .from('user_profiles')
    //     .select('*')
    //     .eq('id', userId)
    //     .single();

    //   if (profileError) {
    //     console.error('Error fetching user profile:', profileError);
    //     return;
    //   }

    //   setProfile(profileData);

    //   // Fetch workspace data if profile exists
    //   if (profileData?.workspace_id) {
    //     const { data: workspaceData, error: workspaceError } = await supabase
    //       .from('workspaces')
    //       .select('*')
    //       .eq('id', profileData.workspace_id)
    //       .single();

    //     if (workspaceError) {
    //       console.error('Error fetching workspace:', workspaceError);
    //       return;
    //     }

    //     setWorkspace(workspaceData);
    //   }
    // } catch (error) {
    //   console.error('Error fetching user data:', error);
    //   setError(createAuthError(error));
    // }
  }, [createAuthError]);

  // Sign up function using two-token strategy
  const signUp = useCallback(async (data: SignUpData) => {
    try {
      setLoading(true);
      setError(null);

      // Prepare signup request for API
      const signUpRequest: SignUpRequest = {
        email: data.email,
        password: data.password,
        confirmPassword: data.confirmPassword,
        firstName: data.first_name,
        lastName: data.last_name,
        companyName: data.company_name,
        companyType: data.company_type,
        phone: data.phone,
        licenseNumber: data.license_number,
        website: data.website,
        address: data.address,
        termsAccepted: data.terms_accepted,
        marketingConsent: data.marketing_consent,
      };

      // Call the API signup endpoint
      const response = await apiClient.signUp(signUpRequest);

      // Store tokens using two-token strategy
      if (response.session) {
        // Store refresh token in httpOnly cookie
        storeRefreshToken(response.session.refreshToken);
        
        // Store access token in memory only
        setAccessToken(response.session.accessToken);
      }

      // Store user data in memory only
      setUser(response.user as any); // Type cast for compatibility
      setProfile(response.profile as any); // Type cast for compatibility  
      setWorkspace(response.workspace as any); // Type cast for compatibility

      console.log('Signup successful');
      
    } catch (error) {
      console.error('Sign up error:', error);
      
      // Handle API errors
      if (isApiError(error)) {
        // Map API error status codes to our error codes
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 409) {
          errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
        } else if (error.status === 400) {
          errorCode = AuthErrorCode.WEAK_PASSWORD;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createAuthError, storeRefreshToken]);

  // Helper function to create workspace and profile
  const createWorkspaceAndProfile = useCallback(async (user: any, signUpData: SignUpData) => {
    // DISABLED: Direct Supabase calls removed to prevent fetches on signin
    // The API client handles all workspace and profile creation instead
    console.log('createWorkspaceAndProfile called but disabled - using API client instead');
    
    // try {
    //   // Create workspace first with comprehensive data
    //   const { data: workspaceData, error: workspaceError } = await supabase
    //     .from('workspaces')
    //     .insert({
    //       company_name: signUpData.company_name,
    //       company_type: signUpData.company_type,
    //       address: signUpData.address || null,
    //       website: signUpData.website || null,
    //       phone: signUpData.phone || null,
    //       status: 'trial',
    //       subscription_plan: 'trial',
    //       primary_color: '#3B82F6', // Default blue color
    //       specialties: [], // Empty array initially
    //       target_markets: [], // Empty array initially
    //       onboarding_completed: false,
    //       onboarding_step: 1,
    //       trial_ends_at: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
    //     })
    //     .select()
    //     .single();

    //   if (workspaceError) {
    //     console.error('Workspace creation error:', workspaceError);
    //     throw new Error(`Failed to create workspace: ${workspaceError.message}`);
    //   }

    //   // Create user profile with comprehensive data
    //   const { error: profileError } = await supabase
    //     .from('user_profiles')
    //     .insert({
    //       id: user.id,
    //       workspace_id: workspaceData.id,
    //       email: user.email,
    //       first_name: signUpData.first_name,
    //       last_name: signUpData.last_name,
    //       role: 'owner',
    //       phone: signUpData.phone || null,
    //       license_number: signUpData.license_number || null,
    //       specialties: [], // Empty array initially
    //       is_active: true,
    //       joined_at: new Date().toISOString(),
    //     });

    //   if (profileError) {
    //     console.error('Profile creation error:', profileError);
    //     // If profile creation fails, we should clean up the workspace
    //     await supabase.from('workspaces').delete().eq('id', workspaceData.id);
    //     throw new Error(`Failed to create user profile: ${profileError.message}`);
    //   }

    //   // Fetch the created data
    //   await fetchUserData(user.id);

    // } catch (error) {
    //   console.error('Error creating workspace and profile:', error);
    //   throw error;
    // }
  }, [fetchUserData]);

  // Sign in function with optional workspace selection using two-token strategy
  const signIn = useCallback(async (email: string, password: string, workspaceId?: string) => {
    try {
      setLoading(true);
      setError(null);

      // Use the API sign in endpoint
      const response = await apiClient.signIn({
        email,
        password,
      });

      // Store tokens using two-token strategy
      if (response.session) {
        // Store refresh token in httpOnly cookie
        storeRefreshToken(response.session.refreshToken);
        
        // Store access token in memory only
        setAccessToken(response.session.accessToken);
      }

      // Store user data in memory only
      setUser(response.user as any); // Type cast for compatibility
      setProfile(response.profile as any); // Type cast for compatibility  
      setWorkspace(response.workspace as any); // Type cast for compatibility

      console.log('Sign in successful');
      
    } catch (error) {
      console.error('Sign in error:', error);
      
      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 401) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS;
        } else if (error.status === 403) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS; // Use closest available code for access denied
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createAuthError, storeRefreshToken]);

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to sign out from API using the refresh token from cookie
      const refreshToken = getRefreshToken();
      if (refreshToken) {
        try {
          await apiClient.signOut({ refreshToken });
        } catch (apiError) {
          console.warn('API signout failed, continuing with cleanup:', apiError);
        }
      }

      // Clear refresh token cookie
      clearRefreshToken();
      
      // Clear local state
      clearAuthData();

    } catch (error) {
      console.error('Sign out error:', error);
      setError(createAuthError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createAuthError, getRefreshToken, clearRefreshToken, clearAuthData]);

  // Switch workspace function
  const switchWorkspace = useCallback(async (workspaceId: string) => {
    if (!accessToken) {
      setError(createAuthError('No access token available', AuthErrorCode.UNKNOWN_ERROR));
      throw new Error('No access token available');
    }

    try {
      setLoading(true);
      setError(null);

      // Call the switch workspace API
      const response = await apiClient.switchWorkspace(accessToken, { workspace_id: workspaceId });

      // Update tokens if provided
      if (response.session) {
        storeRefreshToken(response.session.refresh_token);
        setAccessToken(response.session.access_token);
      }

      // Update user data in memory - map API response to local types
      const mappedProfile: UserProfile = {
        id: response.profile.id,
        workspace_id: response.profile.workspaceId,
        email: response.profile.email,
        first_name: response.profile.firstName,
        last_name: response.profile.lastName,
        role: response.profile.role as any,
        phone: null,
        license_number: null,
        bio: null,
        avatar_url: null,
        specialties: [],
        is_active: true,
        invited_at: null,
        joined_at: new Date().toISOString(),
        invited_by: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const mappedWorkspace: Workspace = {
        id: response.workspace.id,
        company_name: response.workspace.companyName,
        company_type: response.workspace.companyType as any,
        subscription_plan: response.workspace.subscriptionPlan as any,
        domain: null,
        logo_url: null,
        primary_color: '#3B82F6',
        address: null,
        phone: null,
        website: null,
        license_number: null,
        specialties: [],
        target_markets: [],
        status: response.workspace.status as any,
        trial_ends_at: null,
        onboarding_completed: true,
        onboarding_step: 1,
        created_at: response.workspace.createdAt,
        updated_at: new Date().toISOString(),
      };

      setProfile(mappedProfile);
      setWorkspace(mappedWorkspace);

      console.log('Workspace switch successful');

    } catch (error) {
      console.error('Workspace switch error:', error);
      
      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 401) {
          errorCode = AuthErrorCode.UNKNOWN_ERROR;
        } else if (error.status === 403) {
          errorCode = AuthErrorCode.UNKNOWN_ERROR;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [accessToken, createAuthError, storeRefreshToken]);

  // Refresh session function
  const refreshSession = useCallback(async () => {
    try {
      setError(null);

      // Get refresh token from cookie
      const refreshToken = getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token found');
      }

      // Use the API refresh endpoint
      const refreshResponse = await apiClient.refreshToken({
        refreshToken: refreshToken,
      });

      console.log('Refresh API response:', {
        hasAccessToken: !!refreshResponse.accessToken,
        hasRefreshToken: !!refreshResponse.refreshToken,
        refreshTokenValue: refreshResponse.refreshToken,
        accessTokenLength: refreshResponse.accessToken?.length || 0,
        responseKeys: Object.keys(refreshResponse),
        fullResponse: refreshResponse
      });

      // Handle the API response field naming (snake_case vs camelCase)
      const accessToken = refreshResponse.accessToken || refreshResponse.access_token;
      const newRefreshToken = refreshResponse.refreshToken || refreshResponse.refresh_token;

      // Store the new refresh token in cookie (only if provided)
      if (newRefreshToken) {
        storeRefreshToken(newRefreshToken);
      } else {
        console.warn('API did not return a new refresh token, keeping existing token');
        // Keep using the existing refresh token (no rotation)
      }
      
      // Store the new access token in memory
      setAccessToken(accessToken);

      // Update user data from refresh response
      setUser(refreshResponse.user as any); // Type cast for compatibility
      setProfile(refreshResponse.profile as any); // Type cast for compatibility  
      setWorkspace(refreshResponse.workspace as any); // Type cast for compatibility

      console.log('Session refreshed successfully');
      return refreshResponse.accessToken;

    } catch (error) {
      console.error('Session refresh error:', error);
      
      // Clear refresh token cookie
      clearRefreshToken();
      
      // Clear auth state
      clearAuthData();
      
      // Handle token expiration specifically
      if (isTokenExpiredError(error)) {
        handleTokenExpiration(error);
        return; // Don't set error or throw, expiration is handled globally
      }
      
      setError(createAuthError(error, AuthErrorCode.SESSION_EXPIRED));
      throw error;
    }
  }, [createAuthError, getRefreshToken, storeRefreshToken, clearRefreshToken, clearAuthData]);

  // Initialize auth state and set up auth state listener
  useEffect(() => {
    let mounted = true;

    // Get initial session
    const initializeAuth = async () => {
      try {
        // Check for refresh token in cookie
        const refreshToken = getRefreshToken();
        
        if (refreshToken) {
          try {
            // Try to refresh the session (this will also restore user data)
            await refreshSession();
            
            console.log('Session restored via refresh token');
          } catch (refreshError) {
            console.warn('Session refresh failed:', refreshError);
            clearRefreshToken();
          }
        }
        
        if (mounted) {
          setLoading(false);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setError(createAuthError(error));
          setLoading(false);
        }
      }
    };

    initializeAuth();

    return () => {
      mounted = false;
    };
  }, [createAuthError, refreshSession, getRefreshToken, clearRefreshToken]);

  // Auto-refresh session before expiration
  useEffect(() => {
    if (!user || !accessToken) return;

    // Set up token expiry check
    // JWT tokens typically expire after some time (e.g., 15 minutes)
    // We'll refresh 5 minutes before expiration
    const refreshInterval = setInterval(async () => {
      try {
        // For JWT tokens, we can decode them to check expiration
        // But since we don't have direct access to expiry, we'll refresh every 10 minutes
        await refreshSession();
      } catch (error) {
        console.error('Auto-refresh error:', error);
      }
    }, 10 * 60 * 1000); // Refresh every 10 minutes

    return () => clearInterval(refreshInterval);
  }, [user, accessToken, refreshSession]);

  const value: AuthContextType = {
    user,
    profile,
    workspace,
    loading,
    error,
    accessToken,
    signIn,
    signUp,
    signOut,
    refreshSession,
    switchWorkspace,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};