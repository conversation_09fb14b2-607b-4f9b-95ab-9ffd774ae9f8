import React, { useEffect } from 'react';
import { useAuth } from './AuthContext';
import { useTokenExpiration } from '@/hooks/useTokenExpiration';
import { AuthErrorCode } from '@/types';

interface AuthWithExpirationProps {
  children: React.ReactNode;
}

export const AuthWithExpiration: React.FC<AuthWithExpirationProps> = ({ children }) => {
  const { error } = useAuth();
  const { handleTokenExpiration, isTokenExpiredError } = useTokenExpiration();

  useEffect(() => {
    if (error && isTokenExpiredError(error)) {
      handleTokenExpiration(error);
    }
  }, [error, handleTokenExpiration, isTokenExpiredError]);

  return <>{children}</>;
}; 