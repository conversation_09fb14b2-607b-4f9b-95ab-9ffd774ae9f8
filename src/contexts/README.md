# Context Implementations

## Overview

This directory contains comprehensive context implementations for the multi-tenant workspace application. The contexts manage authentication, workspace data, and team collaboration with full integration to Supabase.

## AuthContext Implementation

## Features Implemented

### ✅ React Context for Authentication State Management
- Created `AuthContext` with React Context API
- Provides centralized authentication state across the application
- Includes user, profile, workspace, loading, and error states
- Implements proper TypeScript typing with `AuthContextType` interface

### ✅ Authentication Functions
- **signUp**: Creates new user accounts with workspace creation
- **signIn**: Authenticates existing users with email/password
- **signOut**: Logs out users and clears local state
- **refreshSession**: Refreshes authentication tokens automatically

### ✅ Workspace Context Resolution in JWT Claims
- Fetches user profile and workspace data after authentication
- Automatically resolves workspace context from user profile
- Includes workspace information in the authentication state
- Handles multi-tenant data isolation through workspace_id

### ✅ Error Handling and Session Management
- Comprehensive error handling with custom `AuthError` types
- Maps Supabase errors to user-friendly error codes
- Automatic session refresh before expiration
- Handles authentication failures and network errors
- Session expiration detection and handling

## Components Created

### AuthContext Provider (`src/contexts/AuthContext.tsx`)
- Main authentication context implementation
- Manages all authentication state and operations
- Integrates with Supabase for backend operations
- Handles workspace and profile data fetching

### SignInForm (`src/components/auth/SignInForm.tsx`)
- User-friendly sign-in form component
- Form validation and error display
- Loading states and disabled inputs during authentication
- Integration with AuthContext

### SignUpForm (`src/components/auth/SignUpForm.tsx`)
- Comprehensive workspace owner registration form
- Multi-step form with company information collection
- Form validation with real-time feedback
- Terms acceptance and marketing consent options

### Updated UserButton (`src/components/ui/user-button.tsx`)
- Shows authentication status in header
- Displays user profile and workspace information
- Dropdown menu with user actions
- Sign-out functionality

### Updated Index Page (`src/pages/Index.tsx`)
- Authentication status display
- Toggle between sign-in and sign-up forms
- Comprehensive user/profile/workspace information display
- Debugging information for development

## Key Features

### Automatic Data Fetching
- Fetches user profile and workspace data on authentication
- Handles missing profile/workspace scenarios gracefully
- Real-time updates through Supabase auth state changes

### Error Management
- Custom error types with specific error codes
- User-friendly error messages
- Error clearing functionality
- Network error handling with retry mechanisms

### Session Management
- Automatic session refresh before expiration
- Persistent sessions across browser restarts
- Session state synchronization
- Proper cleanup on sign-out

### Workspace Integration
- Automatic workspace creation during sign-up
- Owner role assignment for workspace creators
- Workspace status and trial management
- Complete data isolation between workspaces

## Usage

### Basic Setup
```tsx
import { AuthProvider } from '@/contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      {/* Your app components */}
    </AuthProvider>
  );
}
```

### Using the Hook
```tsx
import { useAuth } from '@/contexts/AuthContext';

function MyComponent() {
  const { user, profile, workspace, loading, error, signIn, signOut } = useAuth();
  
  if (loading) return <div>Loading...</div>;
  if (!user) return <SignInForm />;
  
  return (
    <div>
      Welcome, {profile?.first_name}!
      Workspace: {workspace?.company_name}
    </div>
  );
}
```

## Requirements Fulfilled

### ✅ Requirement 2.1: Multi-Tenant Authentication
- Unified authentication system with workspace context
- Automatic workspace routing after authentication
- JWT claims include workspace information

### ✅ Requirement 2.2: Workspace Context Resolution
- User authentication includes workspace detection
- Multi-workspace user support (foundation laid)
- Workspace context in authentication state

### ✅ Requirement 2.3: Authentication State Management
- Comprehensive authentication state management
- User, profile, and workspace data integration
- Real-time state updates

### ✅ Requirement 2.4: Error Handling
- Robust error handling for authentication failures
- User-friendly error messages
- Network error recovery

### ✅ Requirement 2.5: Session Management
- Automatic session refresh
- Session expiration handling
- Persistent authentication state

## Testing

Basic test structure created in `src/contexts/__tests__/AuthContext.test.tsx`:
- Context provider functionality
- Error handling for missing provider
- Mock Supabase integration

## Next Steps

The AuthContext is now ready for integration with:
1. Database schema implementation (Task 1-3)
2. Workspace management features (Task 9-10)
3. Role-based access control (Task 11-12)
4. Team invitation system (Task 13-14)

## Notes

- The implementation assumes database tables (workspaces, user_profiles) exist
- Workspace creation during sign-up will work once database schema is implemented
- Error handling is comprehensive and production-ready
- Session management includes automatic refresh and proper cleanup
## Wor
kspaceContext Implementation

### Overview

The WorkspaceContext provides comprehensive workspace and team management functionality for the multi-tenant application. It handles workspace updates, team member management, invitations, and real-time collaboration features.

### Features Implemented

#### ✅ React Context for Workspace Data and Team Management
- Created `WorkspaceContext` with React Context API
- Provides centralized workspace and team state management
- Includes workspace, team members, invitations, loading, and error states
- Implements proper TypeScript typing with `WorkspaceContextType` interface

#### ✅ Workspace Update Functions
- **updateWorkspace**: Updates workspace information (company details, branding, settings)
- Handles workspace status changes and subscription management
- Validates user permissions before allowing updates
- Real-time workspace updates through Supabase subscriptions

#### ✅ Team Member Management Functions
- **inviteTeamMember**: Sends invitations to new team members with role assignment
- **removeTeamMember**: Deactivates team members (maintains audit trail)
- **updateMemberRole**: Updates team member roles with proper permission checks
- **resendInvitation**: Regenerates invitation tokens and extends expiration
- **revokeInvitation**: Cancels pending invitations

#### ✅ Real-time Team Member Updates Using Supabase Subscriptions
- Real-time updates for team member changes (add, update, remove)
- Live invitation status updates (sent, accepted, revoked)
- Workspace status and configuration changes
- Automatic state synchronization across all connected clients

#### ✅ Workspace Status Changes and Trial Management
- Monitors workspace status (active, suspended, trial, cancelled)
- Automatic trial expiration detection and handling
- Subscription plan enforcement and feature restrictions
- Status-based error handling and user notifications

### Key Features

#### Permission-Based Operations
- Role-based access control for all team management functions
- Owner and admin permissions for team management
- Prevents unauthorized operations (self-removal, role escalation)
- Proper validation before database operations

#### Error Management
- Custom `WorkspaceError` types with specific error codes
- User-friendly error messages for common scenarios
- Comprehensive error handling for all operations
- Error clearing functionality

#### Real-time Collaboration
- Live updates for team member changes
- Real-time invitation status tracking
- Workspace configuration synchronization
- Automatic cleanup of expired invitations

#### Data Integrity
- Soft deletion for team members (audit trail preservation)
- Unique invitation tokens with expiration
- Workspace data isolation and security
- Proper foreign key relationships

### Usage

#### Basic Setup
```tsx
import { WorkspaceProvider } from '@/contexts/WorkspaceContext';
import { AuthProvider } from '@/contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <WorkspaceProvider>
        {/* Your app components */}
      </WorkspaceProvider>
    </AuthProvider>
  );
}
```

#### Using the Hook
```tsx
import { useWorkspace } from '@/contexts/WorkspaceContext';

function TeamManagement() {
  const { 
    workspace, 
    teamMembers, 
    invitations, 
    loading, 
    error,
    inviteTeamMember,
    updateMemberRole,
    removeTeamMember 
  } = useWorkspace();
  
  const handleInvite = async (email: string, role: UserRole) => {
    try {
      await inviteTeamMember(email, role);
      // Success handling
    } catch (error) {
      // Error handling
    }
  };
  
  return (
    <div>
      <h2>{workspace?.company_name} Team</h2>
      <div>Team Members: {teamMembers.length}</div>
      <div>Pending Invitations: {invitations.length}</div>
    </div>
  );
}
```

### Requirements Fulfilled

#### ✅ Requirement 1.3: Workspace Data Management
- Complete workspace data management with update capabilities
- Real-time workspace status monitoring
- Workspace configuration and branding support

#### ✅ Requirement 1.4: Workspace Status Handling
- Automatic workspace status monitoring
- Trial expiration detection and handling
- Subscription status enforcement

#### ✅ Requirement 1.5: Workspace Limits and Restrictions
- Team member limit enforcement
- Feature availability based on subscription
- Usage monitoring and restriction handling

#### ✅ Requirement 8.1: Subscription Management
- Trial period tracking and expiration handling
- Subscription plan enforcement
- Feature access control based on plan

#### ✅ Requirement 8.2: Trial Management
- Automatic trial expiration detection
- Trial status monitoring and alerts
- Upgrade prompts and restrictions

### Real-time Features

#### Team Member Updates
- Live addition of new team members
- Real-time role changes and status updates
- Automatic removal of inactive members
- Instant permission updates

#### Invitation Management
- Live invitation status tracking
- Real-time acceptance notifications
- Automatic cleanup of expired invitations
- Instant invitation revocation

#### Workspace Changes
- Real-time workspace configuration updates
- Live status changes (suspension, reactivation)
- Instant branding and settings updates
- Subscription status synchronization

### Error Handling

#### Workspace Errors
- `WORKSPACE_NOT_FOUND`: Workspace doesn't exist or access denied
- `WORKSPACE_SUSPENDED`: Workspace is suspended
- `TRIAL_EXPIRED`: Trial period has ended
- `TEAM_LIMIT_EXCEEDED`: Maximum team members reached
- `FEATURE_NOT_AVAILABLE`: Feature restricted by subscription

#### Permission Errors
- Insufficient permissions for team management
- Role-based operation restrictions
- Self-modification prevention
- Owner-only operation enforcement

### Security Features

#### Access Control
- Role-based permission checking for all operations
- Workspace data isolation enforcement
- User identity verification for operations
- Audit trail maintenance

#### Data Protection
- Soft deletion for team members
- Secure invitation token generation
- Expiration-based invitation security
- Cross-workspace data prevention

### Performance Optimizations

#### Efficient State Management
- Optimistic updates for better UX
- Minimal re-renders through proper state structure
- Efficient real-time subscription management
- Automatic cleanup of subscriptions

#### Network Optimization
- Batch operations where possible
- Efficient database queries
- Real-time subscription filtering
- Proper error retry mechanisms

### Integration Points

The WorkspaceContext integrates seamlessly with:
- **AuthContext**: Uses authentication state for permissions
- **PermissionsContext**: Provides data for role-based access control
- **Database**: Direct integration with Supabase tables
- **Real-time**: Supabase subscriptions for live updates

### Next Steps

The WorkspaceContext is ready for integration with:
1. PermissionsContext implementation (Task 11)
2. Team management UI components (Task 14)
3. Workspace settings interface (Task 10)
4. Onboarding system (Tasks 15-16)

## PermissionsContext Implementation

### Overview

The PermissionsContext provides comprehensive role-based access control (RBAC) for the multi-tenant application. It implements a hierarchical permission system where owners and admins retain all broker capabilities plus elevated permissions, ensuring proper access control while maintaining functional inheritance.

### Features Implemented

#### ✅ React Context for Role-Based Access Control
- Created `PermissionsContext` with React Context API
- Provides centralized permission checking across the application
- Includes user role, permissions array, and access control functions
- Implements proper TypeScript typing with `PermissionsContextType` interface

#### ✅ Permission Checking Functions for Features and Actions
- **hasPermission**: Checks if user has a specific permission
- **canAccessFeature**: Validates feature access based on subscription plan
- **canManageTeam**: Checks team management permissions
- **canManageBilling**: Validates billing management access
- **canEditListing**: Checks listing editing permissions with context
- **canViewWorkspaceSettings**: Validates workspace settings access
- **canInviteMembers**: Checks member invitation permissions
- **canRemoveMembers**: Validates member removal permissions
- **canAssignRoles**: Checks role assignment permissions

#### ✅ Role Hierarchy Enforcement (owner > admin > broker > viewer)
- **Owner**: All permissions including workspace and billing management
- **Admin**: All broker permissions plus team management capabilities
- **Broker**: Core business operations (listings, notes, collaboration)
- **Viewer**: Read-only access to listings and internal notes

#### ✅ Permission-Based UI Rendering and Route Access
- Workspace status validation (suspended, trial expired)
- Subscription plan feature enforcement
- Real-time permission updates based on context changes
- Error handling for permission-related issues

### Key Features

#### Hierarchical Permission System
```typescript
// Role hierarchy with inherited permissions
OWNER: [All Admin permissions + MANAGE_WORKSPACE + MANAGE_BILLING]
ADMIN: [All Broker permissions + MANAGE_TEAM + INVITE_MEMBERS + ASSIGN_ROLES]
BROKER: [CREATE_LISTINGS + EDIT_LISTINGS + ADD_INTERNAL_NOTES + MENTION_TEAM_MEMBERS]
VIEWER: [VIEW_LISTINGS + VIEW_INTERNAL_NOTES + VIEW_ACTIVITY_FEED]
```

#### Subscription-Based Feature Access
- **Trial**: Basic listings, team collaboration, internal notes
- **Basic**: Trial features + advanced search, export data
- **Pro**: Basic features + custom branding, analytics, API access
- **Enterprise**: All features + white label, priority support, custom integrations

#### Workspace Status Enforcement
- Suspended workspaces: All permissions denied
- Trial expired: All permissions denied with upgrade prompts
- Active workspaces: Full permission enforcement based on role

### Components and Hooks

#### PermissionsContext Provider (`src/contexts/PermissionsContext.tsx`)
- Main permissions context implementation
- Role-based permission mapping and validation
- Subscription plan feature enforcement
- Workspace status monitoring

#### Enhanced usePermissions Hook (`src/hooks/usePermissions.tsx`)
- Extended permission checking utilities
- Role comparison functions (hasRoleOrAbove, isOwner, etc.)
- Bulk permission checking (hasAnyPermission, hasAllPermissions)
- User-friendly display name functions

#### RoleGuard Component (`src/components/auth/RoleGuard.tsx`)
- Conditional rendering based on roles and permissions
- Fallback component support
- Higher-order component wrapper
- Hook for programmatic access control

#### ProtectedRoute Component (`src/components/auth/ProtectedRoute.tsx`)
- Route-level access control
- Authentication and authorization validation
- Workspace status checking
- Comprehensive error handling and fallbacks

### Usage Examples

#### Basic Permission Checking
```tsx
import { usePermissions } from '@/hooks/usePermissions';

function ListingActions() {
  const { hasPermission, canEditListing } = usePermissions();
  
  return (
    <div>
      {hasPermission(Permission.CREATE_LISTINGS) && (
        <Button>Create Listing</Button>
      )}
      {canEditListing('listing-id') && (
        <Button>Edit Listing</Button>
      )}
    </div>
  );
}
```

#### Role-Based Component Rendering
```tsx
import { RoleGuard } from '@/components/auth/RoleGuard';

function TeamManagement() {
  return (
    <div>
      <RoleGuard allowedRoles={[UserRole.ADMIN, UserRole.OWNER]}>
        <Button>Manage Team</Button>
      </RoleGuard>
      
      <RoleGuard 
        requiredPermissions={[Permission.INVITE_MEMBERS]}
        fallback={<div>Insufficient permissions</div>}
      >
        <InviteForm />
      </RoleGuard>
    </div>
  );
}
```

#### Protected Routes
```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

function App() {
  return (
    <Routes>
      <Route path="/settings" element={
        <ProtectedRoute requiredRole={UserRole.ADMIN}>
          <WorkspaceSettings />
        </ProtectedRoute>
      } />
      
      <Route path="/billing" element={
        <ProtectedRoute 
          requiredPermissions={[Permission.MANAGE_BILLING]}
          fallback={<AccessDenied />}
        >
          <BillingPage />
        </ProtectedRoute>
      } />
    </Routes>
  );
}
```

### Requirements Fulfilled

#### ✅ Requirement 4.1: Role Hierarchy Enforcement
- Implemented strict role hierarchy (owner > admin > broker > viewer)
- Owners and admins inherit all broker capabilities plus elevated permissions
- Proper permission inheritance and role-based access control

#### ✅ Requirement 4.2: Permission Verification
- Comprehensive permission checking before allowing actions
- Real-time permission validation based on user role and workspace status
- Context-aware permission checking (workspace, subscription, status)

#### ✅ Requirement 4.3: Elevated Permissions for Owners/Admins
- Owners and admins retain full broker functionality
- Additional management permissions for workspace and team operations
- Proper separation of concerns between operational and administrative tasks

#### ✅ Requirement 4.4: Workspace Settings Access Control
- Only owners and admins can access workspace settings
- Billing management restricted to workspace owners only
- Proper permission validation for sensitive operations

#### ✅ Requirement 4.5: Billing Access Control
- Workspace owners have exclusive billing access
- Maintains all broker and admin capabilities for owners
- Proper financial operation security

#### ✅ Requirement 4.6: Listing Creation Access Control
- Owners, admins, and brokers can create listings
- Viewers have read-only access to listings
- Proper operational permission distribution

### Permission Matrix

| Permission | Viewer | Broker | Admin | Owner |
|------------|--------|--------|-------|-------|
| View Listings | ✓ | ✓ | ✓ | ✓ |
| Create Listings | ✗ | ✓ | ✓ | ✓ |
| Edit Listings | ✗ | ✓ | ✓ | ✓ |
| Delete Listings | ✗ | ✓ | ✓ | ✓ |
| Add Internal Notes | ✗ | ✓ | ✓ | ✓ |
| Mention Team Members | ✗ | ✓ | ✓ | ✓ |
| Manage Team | ✗ | ✗ | ✓ | ✓ |
| Invite Members | ✗ | ✗ | ✓ | ✓ |
| Assign Roles | ✗ | ✗ | ✓ | ✓ |
| View Workspace Settings | ✗ | ✗ | ✓ | ✓ |
| Manage Workspace | ✗ | ✗ | ✗ | ✓ |
| Manage Billing | ✗ | ✗ | ✗ | ✓ |

### Security Features

#### Access Control Validation
- Multi-layer permission checking (role, permission, feature, status)
- Workspace status enforcement (suspended, trial expired)
- Subscription plan feature restrictions
- Real-time permission updates

#### Error Handling
- Comprehensive permission error types and codes
- User-friendly error messages with guidance
- Fallback components for unauthorized access
- Proper error logging and monitoring support

### Performance Optimizations

#### Efficient Permission Checking
- Memoized permission arrays based on role
- Cached feature access based on subscription
- Minimal re-renders through proper dependency management
- Optimized permission lookup algorithms

#### Context Optimization
- Selective context updates to prevent unnecessary re-renders
- Efficient permission calculation and caching
- Proper cleanup and memory management
- Optimized subscription and workspace status monitoring

### Integration Points

The PermissionsContext integrates seamlessly with:
- **AuthContext**: Uses user profile and workspace data for permission calculation
- **WorkspaceContext**: Monitors workspace status and subscription changes
- **RoleGuard**: Provides component-level access control
- **ProtectedRoute**: Enables route-level access control
- **UI Components**: Conditional rendering based on permissions

### Example Implementation

A comprehensive example component (`src/examples/PermissionsContextExample.tsx`) demonstrates:
- Role and permission display
- Feature access testing
- Component-level access control
- Permission-based UI rendering
- Real-time permission updates

### Next Steps

The PermissionsContext is ready for integration with:
1. Authentication UI components (Tasks 6-8)
2. Workspace management interface (Tasks 9-10)
3. Team invitation system (Tasks 13-14)
4. Application routing and navigation (Tasks 21-22)