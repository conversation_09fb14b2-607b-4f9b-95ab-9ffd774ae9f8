import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Clock, CreditCard, LogOut, Building2 } from 'lucide-react';

export const TrialExpired: React.FC = () => {
  const { signOut, workspace } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleUpgrade = () => {
    // Navigate to billing/upgrade page
    // This would be implemented when billing is set up
    console.log('Navigate to upgrade page');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-orange-100 flex items-center justify-center">
            <Clock className="h-6 w-6 text-orange-600" />
          </div>
          <CardTitle className="text-orange-900">Trial Period Expired</CardTitle>
          <CardDescription>
            Your 14-day free trial has ended
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Building2 className="h-4 w-4" />
            <AlertDescription>
              The trial period for "{workspace?.company_name}" has expired. 
              Upgrade to a paid plan to continue using all features.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              With a paid plan, you'll get:
            </p>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Unlimited listings and team members</li>
              <li>• Advanced collaboration tools</li>
              <li>• Priority customer support</li>
              <li>• Custom branding and reports</li>
            </ul>
          </div>

          <div className="flex flex-col gap-2">
            <Button className="w-full" onClick={handleUpgrade}>
              <CreditCard className="mr-2 h-4 w-4" />
              Upgrade Now
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={handleSignOut}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </Button>
          </div>

          <p className="text-xs text-center text-muted-foreground">
            Need more time to decide? Contact our sales team for an extended trial.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};