import React, { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Upload, Save, AlertCircle, Check, X, CreditCard, Users, Palette, Building2 } from 'lucide-react';
import { useWorkspace } from '@/contexts/WorkspaceContext';
import { Spinner } from '@/components/ui/spinner';
import { usePermissions } from '@/hooks/usePermissions';
import { 
  Workspace, 
  CompanyType, 
  SubscriptionPlan, 
  WorkspaceStatus 
} from '@/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RoleGuard } from '@/components/auth/RoleGuard';
import { TeamManagement } from './TeamManagement';
import { UserRole } from '@/types';

// Validation schemas
const companyInfoSchema = z.object({
  company_name: z.string().min(1, 'Company name is required'),
  company_type: z.nativeEnum(CompanyType),
  address: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
  license_number: z.string().optional(),
  specialties: z.array(z.string()),
  target_markets: z.array(z.string()),
});

const brandingSchema = z.object({
  primary_color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  logo_url: z.string().optional(),
});

type CompanyInfoFormData = z.infer<typeof companyInfoSchema>;
type BrandingFormData = z.infer<typeof brandingSchema>;

interface WorkspaceSettingsProps {
  className?: string;
}

export const WorkspaceSettings: React.FC<WorkspaceSettingsProps> = ({ className }) => {
  const { workspace, updateWorkspace, loading, error } = useWorkspace();
  const { canViewWorkspaceSettings, canManageBilling } = usePermissions();
  const [activeTab, setActiveTab] = useState('company');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Company info form
  const companyForm = useForm<CompanyInfoFormData>({
    resolver: zodResolver(companyInfoSchema),
    defaultValues: {
      company_name: workspace?.company_name || '',
      company_type: workspace?.company_type || CompanyType.TEAM,
      address: workspace?.address || '',
      phone: workspace?.phone || '',
      website: workspace?.website || '',
      license_number: workspace?.license_number || '',
      specialties: workspace?.specialties || [],
      target_markets: workspace?.target_markets || [],
    },
  });

  // Branding form
  const brandingForm = useForm<BrandingFormData>({
    resolver: zodResolver(brandingSchema),
    defaultValues: {
      primary_color: workspace?.primary_color || '#3B82F6',
      logo_url: workspace?.logo_url || '',
    },
  });

  // Check permissions
  if (!canViewWorkspaceSettings()) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Access Denied</h3>
            <p className="text-muted-foreground">
              You don't have permission to view workspace settings.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle company info form submission
  const onCompanyInfoSubmit = useCallback(async (data: CompanyInfoFormData) => {
    try {
      await updateWorkspace(data);
      // Show success message (you might want to add a toast notification here)
    } catch (error) {
      console.error('Failed to update company information:', error);
    }
  }, [updateWorkspace]);

  // Handle branding form submission
  const onBrandingSubmit = useCallback(async (data: BrandingFormData) => {
    try {
      await updateWorkspace(data);
      // Show success message
    } catch (error) {
      console.error('Failed to update branding:', error);
    }
  }, [updateWorkspace]);

  // Handle logo upload
  const handleLogoUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setUploadError('Please select an image file');
      return;
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setUploadError('File size must be less than 2MB');
      return;
    }

    try {
      setIsUploading(true);
      setUploadError(null);

      // TODO: Implement actual file upload to Supabase storage
      // For now, we'll create a placeholder URL
      const logoUrl = URL.createObjectURL(file);
      
      brandingForm.setValue('logo_url', logoUrl);
      await updateWorkspace({ logo_url: logoUrl });

    } catch (error) {
      console.error('Logo upload failed:', error);
      setUploadError('Failed to upload logo. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, [brandingForm, updateWorkspace]);

  // Handle specialty/market management
  const addSpecialty = useCallback((specialty: string) => {
    if (!specialty.trim()) return;
    const currentSpecialties = companyForm.getValues('specialties');
    if (!currentSpecialties.includes(specialty)) {
      companyForm.setValue('specialties', [...currentSpecialties, specialty]);
    }
  }, [companyForm]);

  const removeSpecialty = useCallback((specialty: string) => {
    const currentSpecialties = companyForm.getValues('specialties');
    companyForm.setValue('specialties', currentSpecialties.filter(s => s !== specialty));
  }, [companyForm]);

  const addTargetMarket = useCallback((market: string) => {
    if (!market.trim()) return;
    const currentMarkets = companyForm.getValues('target_markets');
    if (!currentMarkets.includes(market)) {
      companyForm.setValue('target_markets', [...currentMarkets, market]);
    }
  }, [companyForm]);

  const removeTargetMarket = useCallback((market: string) => {
    const currentMarkets = companyForm.getValues('target_markets');
    companyForm.setValue('target_markets', currentMarkets.filter(m => m !== market));
  }, [companyForm]);

  if (!workspace) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Workspace</h3>
            <p className="text-muted-foreground">
              No workspace data available.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Workspace Settings</h1>
          <p className="text-muted-foreground">
            Manage your workspace configuration, branding, and subscription.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">{error.message}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Settings Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="company" className="flex items-center space-x-2">
              <Building2 className="h-4 w-4" />
              <span>Company</span>
            </TabsTrigger>
            <TabsTrigger value="branding" className="flex items-center space-x-2">
              <Palette className="h-4 w-4" />
              <span>Branding</span>
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Team</span>
            </TabsTrigger>
            <TabsTrigger 
              value="billing" 
              className="flex items-center space-x-2"
              disabled={!canManageBilling()}
            >
              <CreditCard className="h-4 w-4" />
              <span>Billing</span>
            </TabsTrigger>
          </TabsList>

          {/* Company Information Tab */}
          <TabsContent value="company" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Company Information</CardTitle>
                <CardDescription>
                  Update your company details and business information.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...companyForm}>
                  <form onSubmit={companyForm.handleSubmit(onCompanyInfoSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={companyForm.control}
                        name="company_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter company name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="company_type"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company Type</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select company type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value={CompanyType.INDIVIDUAL}>Individual</SelectItem>
                                <SelectItem value={CompanyType.TEAM}>Team</SelectItem>
                                <SelectItem value={CompanyType.FIRM}>Firm</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter phone number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="website"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Website</FormLabel>
                            <FormControl>
                              <Input placeholder="https://example.com" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={companyForm.control}
                        name="license_number"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>License Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter license number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={companyForm.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter company address"
                              className="min-h-[80px]"
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Specialties */}
                    <div className="space-y-3">
                      <Label>Specialties</Label>
                      <div className="flex flex-wrap gap-2">
                        {companyForm.watch('specialties').map((specialty) => (
                          <Badge key={specialty} variant="secondary" className="flex items-center gap-1">
                            {specialty}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-transparent"
                              onClick={() => removeSpecialty(specialty)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add specialty"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addSpecialty(e.currentTarget.value);
                              e.currentTarget.value = '';
                            }
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={(e) => {
                            const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                            addSpecialty(input.value);
                            input.value = '';
                          }}
                        >
                          Add
                        </Button>
                      </div>
                    </div>

                    {/* Target Markets */}
                    <div className="space-y-3">
                      <Label>Target Markets</Label>
                      <div className="flex flex-wrap gap-2">
                        {companyForm.watch('target_markets').map((market) => (
                          <Badge key={market} variant="secondary" className="flex items-center gap-1">
                            {market}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-4 w-4 p-0 hover:bg-transparent"
                              onClick={() => removeTargetMarket(market)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Add target market"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addTargetMarket(e.currentTarget.value);
                              e.currentTarget.value = '';
                            }
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={(e) => {
                            const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                            addTargetMarket(input.value);
                            input.value = '';
                          }}
                        >
                          Add
                        </Button>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>   
       {/* Branding Tab */}
          <TabsContent value="branding" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Branding & Customization</CardTitle>
                <CardDescription>
                  Customize your workspace appearance with your company branding.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...brandingForm}>
                  <form onSubmit={brandingForm.handleSubmit(onBrandingSubmit)} className="space-y-6">
                    {/* Logo Upload */}
                    <div className="space-y-4">
                      <Label>Company Logo</Label>
                      <div className="flex items-center space-x-4">
                        {workspace.logo_url && (
                          <div className="w-16 h-16 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden">
                            <img 
                              src={workspace.logo_url} 
                              alt="Company logo" 
                              className="w-full h-full object-contain"
                            />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <Button
                              type="button"
                              variant="outline"
                              disabled={isUploading}
                              onClick={() => document.getElementById('logo-upload')?.click()}
                            >
                              {isUploading ? (
                                <>
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2" />
                                  Uploading...
                                </>
                              ) : (
                                <>
                                  <Upload className="h-4 w-4 mr-2" />
                                  Upload Logo
                                </>
                              )}
                            </Button>
                            {workspace.logo_url && (
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                  brandingForm.setValue('logo_url', '');
                                  updateWorkspace({ logo_url: null });
                                }}
                              >
                                Remove
                              </Button>
                            )}
                          </div>
                          <input
                            id="logo-upload"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleLogoUpload}
                          />
                          <p className="text-sm text-muted-foreground mt-1">
                            Recommended: Square image, max 2MB
                          </p>
                          {uploadError && (
                            <p className="text-sm text-destructive mt-1">{uploadError}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Primary Color */}
                    <FormField
                      control={brandingForm.control}
                      name="primary_color"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primary Color</FormLabel>
                          <div className="flex items-center space-x-4">
                            <FormControl>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="color"
                                  className="w-12 h-10 rounded border border-input cursor-pointer"
                                  {...field}
                                />
                                <Input
                                  placeholder="#3B82F6"
                                  className="w-32"
                                  {...field}
                                />
                              </div>
                            </FormControl>
                            <div 
                              className="w-10 h-10 rounded border-2 border-gray-200"
                              style={{ backgroundColor: field.value }}
                            />
                          </div>
                          <FormDescription>
                            This color will be used for buttons, links, and accents throughout your workspace.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end">
                      <Button type="submit" disabled={loading}>
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Preview Card */}
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  See how your branding will appear in the workspace.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-3 mb-4">
                      {workspace.logo_url && (
                        <img 
                          src={workspace.logo_url} 
                          alt="Logo preview" 
                          className="w-8 h-8 object-contain"
                        />
                      )}
                      <span className="font-semibold">{workspace.company_name}</span>
                    </div>
                    <Button 
                      style={{ backgroundColor: brandingForm.watch('primary_color') }}
                      className="text-white"
                    >
                      Sample Button
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Team Management Tab */}
          <TabsContent value="team" className="space-y-6">
            <TeamManagement />
          </TabsContent>

          {/* Billing & Subscription Tab */}
          <TabsContent value="billing" className="space-y-6">
            <RoleGuard allowedRoles={[UserRole.OWNER]} fallback={
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
                    <p className="text-muted-foreground">
                      Only workspace owners can manage billing and subscription settings.
                    </p>
                  </div>
                </CardContent>
              </Card>
            }>
              {/* Current Plan */}
              <Card>
                <CardHeader>
                  <CardTitle>Current Plan</CardTitle>
                  <CardDescription>
                    Manage your subscription and billing information.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Plan Status */}
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold capitalize">
                            {workspace.subscription_plan} Plan
                          </h3>
                          <Badge 
                            variant={workspace.status === WorkspaceStatus.ACTIVE ? 'success' : 
                                   workspace.status === WorkspaceStatus.TRIAL ? 'accent' : 'destructive'}
                          >
                            {workspace.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at && (
                            `Trial ends ${new Date(workspace.trial_ends_at).toLocaleDateString()}`
                          )}
                          {workspace.status === WorkspaceStatus.ACTIVE && (
                            'Your subscription is active'
                          )}
                          {workspace.status === WorkspaceStatus.SUSPENDED && (
                            'Your workspace has been suspended'
                          )}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">
                          {workspace.subscription_plan === SubscriptionPlan.TRIAL ? 'Free' :
                           workspace.subscription_plan === SubscriptionPlan.BASIC ? '$29' :
                           workspace.subscription_plan === SubscriptionPlan.PRO ? '$79' :
                           workspace.subscription_plan === SubscriptionPlan.ENTERPRISE ? '$199' : 'Free'}
                        </div>
                        {workspace.subscription_plan !== SubscriptionPlan.TRIAL && (
                          <div className="text-sm text-muted-foreground">per month</div>
                        )}
                      </div>
                    </div>

                    {/* Trial Warning */}
                    {workspace.status === WorkspaceStatus.TRIAL && workspace.trial_ends_at && (
                      <Card className="border-orange-200 bg-orange-50">
                        <CardContent className="pt-6">
                          <div className="flex items-center space-x-2 text-orange-800">
                            <AlertCircle className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              Your trial expires in {Math.ceil((new Date(workspace.trial_ends_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days
                            </span>
                          </div>
                          <p className="text-sm text-orange-700 mt-2">
                            Upgrade your plan to continue using all features after your trial ends.
                          </p>
                        </CardContent>
                      </Card>
                    )}

                    {/* Plan Features */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">Basic</CardTitle>
                          <div className="text-2xl font-bold">$29<span className="text-sm font-normal">/mo</span></div>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Up to 5 team members</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">50 active listings</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">5GB storage</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Basic support</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-primary">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg">Pro</CardTitle>
                            <Badge>Popular</Badge>
                          </div>
                          <div className="text-2xl font-bold">$79<span className="text-sm font-normal">/mo</span></div>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Up to 25 team members</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Unlimited listings</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">50GB storage</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Priority support</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Advanced analytics</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">Enterprise</CardTitle>
                          <div className="text-2xl font-bold">$199<span className="text-sm font-normal">/mo</span></div>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Unlimited team members</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Unlimited listings</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">500GB storage</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">24/7 phone support</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Check className="h-4 w-4 text-green-600" />
                            <span className="text-sm">Custom integrations</span>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-center space-x-4">
                      {workspace.subscription_plan === SubscriptionPlan.TRIAL && (
                        <>
                          <Button>
                            <CreditCard className="h-4 w-4 mr-2" />
                            Upgrade to Pro
                          </Button>
                          <Button variant="outline">
                            Start with Basic
                          </Button>
                        </>
                      )}
                      {workspace.subscription_plan !== SubscriptionPlan.TRIAL && (
                        <>
                          <Button variant="outline">
                            Change Plan
                          </Button>
                          <Button variant="outline">
                            Manage Billing
                          </Button>
                        </>
                      )}
                    </div>

                    {/* Billing Information */}
                    {workspace.subscription_plan !== SubscriptionPlan.TRIAL && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Billing Information</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="text-center py-4">
                              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                              <p className="text-muted-foreground">
                                Billing management integration will be implemented with a payment provider.
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </CardContent>
              </Card>
            </RoleGuard>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};