import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { setTokenExpirationHandlers } from '@/lib/token-expiration-handler';

export const TokenExpirationSetup: React.FC = () => {
  const navigate = useNavigate();
  const { toast, dismiss } = useToast();

  useEffect(() => {
    // Set up the global token expiration handlers
    setTokenExpirationHandlers(navigate, toast, dismiss);
  }, [navigate, toast, dismiss]);

  return null; // This component doesn't render anything
}; 