import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';
import { UserRole, Permission } from '@/types';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield } from 'lucide-react';
import { Spinner } from '@/components/ui/spinner';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
  requiredPermissions?: Permission[];
  requireAllPermissions?: boolean; // If true, user must have ALL permissions; if false, ANY permission
  fallback?: React.ReactNode;
  showFallback?: boolean; // If true, shows fallback; if false, renders nothing
}

/**
 * Component that conditionally renders children based on user role and permissions
 */
export const RoleGuard: React.FC<RoleGuardProps> = ({
  children,
  allowedRoles = [],
  requiredPermissions = [],
  requireAllPermissions = false,
  fallback,
  showFallback = true,
}) => {
  const {
    userRole,
    hasRoleOrAbove,
    hasExactRole,
    hasAnyPermission,
    hasAllPermissions,
    loading,
    error,
  } = usePermissions();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Spinner size="md" data-testid="loading-spinner" />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          {error.message}
        </AlertDescription>
      </Alert>
    );
  }

  // Check role-based access
  let hasRoleAccess = true;
  if (allowedRoles.length > 0) {
    hasRoleAccess = allowedRoles.some(role => {
      // Check if user has exact role or higher
      return hasExactRole(role) || hasRoleOrAbove(role);
    });
  }

  // Check permission-based access
  let hasPermissionAccess = true;
  if (requiredPermissions.length > 0) {
    hasPermissionAccess = requireAllPermissions
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);
  }

  // Grant access if both role and permission checks pass
  const hasAccess = hasRoleAccess && hasPermissionAccess;

  if (hasAccess) {
    return <>{children}</>;
  }

  // Show fallback or nothing
  if (!showFallback) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  // Default fallback
  return (
    <Alert>
      <Shield className="h-4 w-4" />
      <AlertDescription>
        You don't have permission to access this feature.
        {allowedRoles.length > 0 && (
          <span className="block mt-1 text-sm text-muted-foreground">
            Required role: {allowedRoles.join(', ')}
          </span>
        )}
        {requiredPermissions.length > 0 && (
          <span className="block mt-1 text-sm text-muted-foreground">
            Required permissions: {requiredPermissions.join(', ')}
          </span>
        )}
      </AlertDescription>
    </Alert>
  );
};

/**
 * Higher-order component for role-based access control
 */
export const withRoleGuard = <P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<RoleGuardProps, 'children'>
) => {
  return (props: P) => (
    <RoleGuard {...guardProps}>
      <Component {...props} />
    </RoleGuard>
  );
};

/**
 * Hook for conditional rendering based on permissions
 */
export const useRoleGuard = (
  allowedRoles: UserRole[] = [],
  requiredPermissions: Permission[] = [],
  requireAllPermissions: boolean = false
) => {
  const {
    userRole,
    hasRoleOrAbove,
    hasExactRole,
    hasAnyPermission,
    hasAllPermissions,
  } = usePermissions();

  // Check role-based access
  let hasRoleAccess = true;
  if (allowedRoles.length > 0) {
    hasRoleAccess = allowedRoles.some(role => {
      return hasExactRole(role) || hasRoleOrAbove(role);
    });
  }

  // Check permission-based access
  let hasPermissionAccess = true;
  if (requiredPermissions.length > 0) {
    hasPermissionAccess = requireAllPermissions
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);
  }

  return {
    hasAccess: hasRoleAccess && hasPermissionAccess,
    hasRoleAccess,
    hasPermissionAccess,
    userRole,
  };
};