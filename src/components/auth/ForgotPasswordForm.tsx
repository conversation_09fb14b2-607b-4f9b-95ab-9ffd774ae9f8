import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { ArrowLeft, Mail } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Spinner } from '@/components/ui/spinner';
import { apiClient } from '@/lib/api-client';

interface ForgotPasswordFormProps {
  isModal?: boolean;
  onClose?: () => void;
  onBackToSignIn?: () => void;
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({ 
  isModal = false, 
  onClose, 
  onBackToSignIn 
}) => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {

      const { success, message } = await apiClient.forgotPassword({ email });

      if (!success) {
        throw new Error(message);
      }

      setSuccess(true);
    } catch (error: any) {
      setError(error.message || 'Failed to send reset email');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToSignIn = () => {
    if (isModal && onBackToSignIn) {
      onBackToSignIn();
    } else {
      navigate('/auth/signin');
    }
  };

  const handleClose = () => {
    if (isModal && onClose) {
      onClose();
    }
  };

  const formContent = success ? (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Mail className="h-5 w-5" />
        <span className="font-semibold">Check Your Email</span>
      </div>
      
      <Alert>
        <Mail className="h-4 w-4" />
        <AlertDescription>
          We've sent password reset instructions to <strong>{email}</strong>. 
          Please check your inbox and follow the link to reset your password.
        </AlertDescription>
      </Alert>
      
      <Button 
        variant="outline" 
        className="w-full"
        onClick={handleBackToSignIn}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Sign In
      </Button>
    </div>
  ) : (
    <form onSubmit={handleSubmit} className="space-y-4">
      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            {error}
          </AlertDescription>
        </Alert>
      )}
      
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          placeholder="Enter your email"
          required
          disabled={loading}
          autoComplete="email"
        />
      </div>
      
      <Button 
        type="submit" 
        className="w-full" 
        disabled={loading || !email}
      >
        {loading ? (
          <>
            <Spinner size="sm" className="mr-2" />
            Sending...
          </>
        ) : (
          'Send Reset Link'
        )}
      </Button>

      <Button 
        type="button"
        variant="outline" 
        className="w-full"
        onClick={handleBackToSignIn}
        disabled={loading}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Sign In
      </Button>
    </form>
  );

  // If used as a modal, render without Card wrapper
  if (isModal) {
    return (
      <div className="space-y-4">
        {formContent}
      </div>
    );
  }

  // Standalone page version with Card wrapper
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>
          {success ? 'Check Your Email' : 'Forgot Password'}
        </CardTitle>
        <CardDescription>
          {success 
            ? 'Password reset instructions sent'
            : 'Enter your email address and we\'ll send you a link to reset your password'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
};

// Modal wrapper component
interface ForgotPasswordModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onBackToSignIn?: () => void;
}

export const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  open,
  onOpenChange,
  onBackToSignIn
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Forgot Password</DialogTitle>
          <DialogDescription>
            Enter your email address and we'll send you a link to reset your password
          </DialogDescription>
        </DialogHeader>
        <ForgotPasswordForm 
          isModal={true}
          onClose={() => onOpenChange(false)}
          onBackToSignIn={() => {
            onOpenChange(false);
            onBackToSignIn?.();
          }}
        />
      </DialogContent>
    </Dialog>
  );
};