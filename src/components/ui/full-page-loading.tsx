import React from 'react';
import { cn } from '@/lib/utils';
import { LoadingState } from './spinner';

interface FullPageLoadingProps {
  message?: string;
  className?: string;
  spinnerSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  showMessage?: boolean;
}

export const FullPageLoading: React.FC<FullPageLoadingProps> = ({
  message = 'Loading...',
  className,
  spinnerSize = 'lg',
  showMessage = true,
}) => {
  return (
    <div className={cn("flex items-center justify-center min-h-screen", className)}>
      <LoadingState
        message={message}
        spinnerSize={spinnerSize}
        showMessage={showMessage}
        className="py-8"
      />
    </div>
  );
}; 