import React from 'react';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'muted' | 'white' | 'destructive';
}

const spinnerSizes = {
  xs: 'h-3 w-3',
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

const spinnerColors = {
  primary: 'text-primary',
  muted: 'text-muted-foreground',
  white: 'text-white',
  destructive: 'text-destructive'
};

export const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  className,
  color = 'primary',
}) => {
  return (
    <Loader2 
      className={cn(
        "animate-spin",
        spinnerSizes[size],
        spinnerColors[color],
        className
      )} 
    />
  );
};

interface LoadingStateProps {
  message?: string;
  className?: string;
  spinnerSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  spinnerColor?: 'primary' | 'muted' | 'white' | 'destructive';
  showMessage?: boolean;
  inline?: boolean;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  className,
  spinnerSize = 'md',
  spinnerColor = 'primary',
  showMessage = true,
  inline = false,
}) => {
  const baseClasses = inline 
    ? "flex items-center gap-2" 
    : "flex flex-col items-center justify-center text-center";

  return (
    <div className={cn(baseClasses, className)}>
      <Spinner size={spinnerSize} color={spinnerColor} />
      {showMessage && (
        <p className={cn(
          "text-muted-foreground",
          inline ? "text-sm" : ""
        )}>
          {message}
        </p>
      )}
    </div>
  );
}; 