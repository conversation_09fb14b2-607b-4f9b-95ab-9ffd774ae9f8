import * as React from "react"
import { User, LogOut, Setting<PERSON> } from "lucide-react"
import { useNavigate } from "react-router-dom"
import { Button } from "./button"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "./avatar"
import { useAuth } from "@/contexts/AuthContext"
import { Spinner } from "./spinner"

export function UserButton() {
  const { user, profile, workspace, loading, signOut } = useAuth();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  if (loading) {
    return (
      <Button 
        variant="ghost" 
        size="icon" 
        className="rounded-full" 
        disabled
      >
        <Spinner size="md" />
      </Button>
    );
  }

  if (!user || !profile) {
    return (
      <Button 
        variant="ghost" 
        size="icon" 
        className="rounded-full" 
        aria-label="Not signed in"
      >
        <User className="h-5 w-5" />
      </Button>
    );
  }

  const initials = `${profile.first_name?.[0] || ''}${profile.last_name?.[0] || ''}`.toUpperCase();
  const displayName = `${profile.first_name} ${profile.last_name}`.trim();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className="relative h-8 w-8 rounded-full"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={profile.avatar_url} alt={displayName} />
            <AvatarFallback>{initials || 'U'}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{displayName}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {profile.email}
            </p>
            {workspace && (
              <p className="text-xs leading-none text-muted-foreground">
                {workspace.company_name} • {profile.role}
              </p>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => navigate('/account')}>
          <Settings className="mr-2 h-4 w-4" />
          <span>Account Settings</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}