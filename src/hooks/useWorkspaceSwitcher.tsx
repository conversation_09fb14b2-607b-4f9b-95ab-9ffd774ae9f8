import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiClient } from '@/lib/api-client';
import type { UserWorkspacesResponse } from '@/lib/api-client';

interface UseWorkspaceSwitcherResult {
  availableWorkspaces: UserWorkspacesResponse['workspaces'];
  loading: boolean;
  error: string | null;
  switchToWorkspace: (workspaceId: string) => Promise<void>;
  refreshWorkspaces: () => Promise<void>;
  isCurrentWorkspace: (workspaceId: string) => boolean;
}

export const useWorkspaceSwitcher = (): UseWorkspaceSwitcherResult => {
  const { accessToken, workspace, switchWorkspace } = useAuth();
  
  const [availableWorkspaces, setAvailableWorkspaces] = useState<UserWorkspacesResponse['workspaces']>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchWorkspaces = useCallback(async () => {
    if (!accessToken) {
      setError('No access token available');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.getUserWorkspaces(accessToken);
      setAvailableWorkspaces(response.workspaces);
    } catch (error: any) {
      console.error('Error fetching workspaces:', error);
      setError(error.message || 'Failed to fetch available workspaces');
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  // Fetch workspaces on mount and when accessToken changes
  useEffect(() => {
    fetchWorkspaces();
  }, [fetchWorkspaces]);

  const switchToWorkspace = useCallback(async (workspaceId: string) => {
    if (!workspaceId || workspaceId === workspace?.id) {
      return;
    }

    try {
      setError(null);
      await switchWorkspace(workspaceId);
      
      // Refresh the workspace list after successful switch
      await fetchWorkspaces();
    } catch (error: any) {
      console.error('Error switching workspace:', error);
      setError(error.message || 'Failed to switch workspace');
      throw error; // Re-throw so components can handle it
    }
  }, [workspace?.id, switchWorkspace, fetchWorkspaces]);

  const refreshWorkspaces = useCallback(async () => {
    await fetchWorkspaces();
  }, [fetchWorkspaces]);

  const isCurrentWorkspace = useCallback((workspaceId: string) => {
    return workspaceId === workspace?.id;
  }, [workspace?.id]);

  return {
    availableWorkspaces,
    loading,
    error,
    switchToWorkspace,
    refreshWorkspaces,
    isCurrentWorkspace,
  };
}; 