// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'https://api.mariogarcia.com' || 'http://localhost:3001',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
} as const

// API Endpoints
export const API_ENDPOINTS = {
  // Health and index
  HEALTH: '/health',
  INDEX: '/',
  API_CLIENT: '/apiClient',

  // Auth endpoints (updated to match new API)
  AUTH: {
    SIGNUP: '/v1/auth/signup',
    SIGNIN: '/v1/auth/signin',
    SIGNOUT: '/v1/auth/signout',
    REFRESH: '/v1/auth/refresh',
    FORGOT_PASSWORD: '/v1/auth/forgot-password',
    RESET_PASSWORD: '/v1/auth/reset-password',
    CALLBACK: '/v1/auth/callback',
  },

  // User endpoints
  USERS: {
    PROFILE: '/profile',
    VERIFY_EMAIL: '/verify-email',
  },

  // Workspace endpoints
  WORKSPACES: {
    CURRENT: '/v1/workspaces/current',
    INVITATIONS: '/v1/workspaces/invitations',
    INVITATION: '/v1/workspaces/invitations/:id',
    RESEND_INVITATION: '/v1/workspaces/invitations/:id/resend',
  },

  // Listing endpoints
  LISTINGS: {
    LIST: '/v1/listings',
    CREATE: '/v1/listings',
    GET: '/v1/listings/:id',
    UPDATE: '/v1/listings/:id',
    DELETE: '/v1/listings/:id',
    BULK_CSV: '/v1/listings/bulk/csv',
  },

  // File endpoints
  FILES: {
    UPLOAD: '/v1/files/upload',
    GET: '/v1/files/:id',
    DELETE: '/v1/files/:id',
  },

  // Logs endpoints
  LOGS: {
    BY_PATH: '/v1/logs/by-path',
    DETAIL: '/v1/logs/:id',
  },
} as const

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const