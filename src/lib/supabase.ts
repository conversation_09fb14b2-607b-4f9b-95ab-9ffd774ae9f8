// import { createClient } from "@supabase/supabase-js";

// const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
// const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// if (!supabaseUrl || !supabaseAnonKey) {
//   throw new Error(
//     "Missing VITE_SUPABASE_URL or VITE_SUPABASE_ANON_KEY environment variables"
//   );
// }

// export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
//   auth: {
//     autoRefreshToken: false, // We handle token refresh manually
//     persistSession: false,   // We don't persist sessions in localStorage
//     detectSessionInUrl: false, // We don't use URL-based auth
//   },
//   // realtime: {
//   //   // Disable realtime features to prevent WebSocket connections
//   //   // This prevents HTTPS/WSS mixed content errors in production
//   //   heartbeatIntervalMs: 0, // Disable heartbeat
//   //   reconnectAfterMs: () => null, // Disable reconnection
//   // },
// });
